document.addEventListener('DOMContentLoaded', () => {
    const addButton = document.getElementById('add-button');
    const deleteButton = document.getElementById('delete-button');
    const choreInput = document.getElementById('chore-input');
    const choreList = document.getElementById('chore-list');
    const themeToggle = document.getElementById('theme-toggle');

    // Global drag state
    let draggedElement = null;

    // Theme management
    const loadTheme = () => {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        updateThemeToggleIcon(savedTheme);
    };

    const toggleTheme = () => {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateThemeToggleIcon(newTheme);
    };

    const updateThemeToggleIcon = (theme) => {
        themeToggle.title = theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode';
    };

    // Generate unique ID for chores
    const generateId = () => {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    };

    // Load chores from local storage
    const loadChores = () => {
        const chores = JSON.parse(localStorage.getItem('chores')) || [];
        choreList.innerHTML = ''; // Clear existing items
        chores.forEach(chore => {
            createChoreElement(chore);
        });
    };

    // Save chores to local storage
    const saveChores = () => {
        const chores = [];
        choreList.querySelectorAll('li').forEach(li => {
            const choreData = {
                id: li.dataset.choreId,
                text: li.querySelector('.chore-text').textContent,
                completed: li.querySelector('.chore-checkbox').checked
            };
            chores.push(choreData);
        });
        localStorage.setItem('chores', JSON.stringify(chores));
    };

    // Create a chore element with all functionality
    const createChoreElement = (chore) => {
        const choreItem = document.createElement('li');
        choreItem.dataset.choreId = chore.id;
        choreItem.className = chore.completed ? 'completed' : '';
        choreItem.draggable = true;

        choreItem.innerHTML = `
            <div class="drag-handle" title="Drag to reorder">⋮⋮</div>
            <input type="checkbox" class="chore-checkbox" ${chore.completed ? 'checked' : ''}>
            <span class="chore-text" contenteditable="false">${chore.text}</span>
            <button class="edit-button" title="Edit chore">✏️</button>
            <button class="delete-individual-button" title="Delete chore">🗑️</button>
        `;

        // Add event listeners for this chore
        addChoreEventListeners(choreItem);
        addDragEventListeners(choreItem);
        choreList.appendChild(choreItem);
    };

    // Add drag-and-drop event listeners
    const addDragEventListeners = (choreItem) => {
        choreItem.addEventListener('dragstart', (e) => {
            draggedElement = choreItem;
            choreItem.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', choreItem.outerHTML);
        });

        choreItem.addEventListener('dragend', () => {
            choreItem.classList.remove('dragging');
            // Clear all drag-over classes from all items
            document.querySelectorAll('.drag-over-top, .drag-over-bottom').forEach(el => {
                el.classList.remove('drag-over-top', 'drag-over-bottom');
            });
            draggedElement = null;
        });

        choreItem.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            if (draggedElement && draggedElement !== choreItem) {
                const rect = choreItem.getBoundingClientRect();
                const midpoint = rect.top + rect.height / 2;

                if (e.clientY < midpoint) {
                    choreItem.classList.add('drag-over-top');
                    choreItem.classList.remove('drag-over-bottom');
                } else {
                    choreItem.classList.add('drag-over-bottom');
                    choreItem.classList.remove('drag-over-top');
                }
            }
        });

        choreItem.addEventListener('dragleave', (e) => {
            // Only remove classes if we're actually leaving the element
            if (!choreItem.contains(e.relatedTarget)) {
                choreItem.classList.remove('drag-over-top', 'drag-over-bottom');
            }
        });

        choreItem.addEventListener('drop', (e) => {
            e.preventDefault();
            choreItem.classList.remove('drag-over-top', 'drag-over-bottom');

            if (draggedElement && draggedElement !== choreItem) {
                const rect = choreItem.getBoundingClientRect();
                const midpoint = rect.top + rect.height / 2;

                if (e.clientY < midpoint) {
                    choreList.insertBefore(draggedElement, choreItem);
                } else {
                    choreList.insertBefore(draggedElement, choreItem.nextSibling);
                }

                // Re-add event listeners to the moved element
                addChoreEventListeners(draggedElement);
                addDragEventListeners(draggedElement);

                saveChores(); // Save new order
            }
        });
    };

    // Add event listeners for individual chore functionality
    const addChoreEventListeners = (choreItem) => {
        const checkbox = choreItem.querySelector('.chore-checkbox');
        const choreText = choreItem.querySelector('.chore-text');
        const editButton = choreItem.querySelector('.edit-button');
        const deleteButton = choreItem.querySelector('.delete-individual-button');

        // Toggle completion status
        checkbox.addEventListener('change', () => {
            choreItem.classList.toggle('completed', checkbox.checked);
            saveChores();
        });

        // Edit chore functionality
        editButton.addEventListener('click', () => {
            if (choreText.contentEditable === 'false') {
                choreText.contentEditable = 'true';
                choreText.focus();
                editButton.textContent = '✅';
                editButton.title = 'Save changes';
            } else {
                choreText.contentEditable = 'false';
                editButton.textContent = '✏️';
                editButton.title = 'Edit chore';
                saveChores();
            }
        });

        // Save on Enter key when editing
        choreText.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                choreText.contentEditable = 'false';
                editButton.textContent = '✏️';
                editButton.title = 'Edit chore';
                saveChores();
            }
        });

        // Delete individual chore
        deleteButton.addEventListener('click', () => {
            if (confirm('Are you sure you want to delete this chore?')) {
                choreItem.remove();
                saveChores();
            }
        });
    };

    // Add new chore
    const addChore = () => {
        const choreText = choreInput.value.trim();
        if (choreText !== '') {
            const newChore = {
                id: generateId(),
                text: choreText,
                completed: false
            };
            createChoreElement(newChore);
            choreInput.value = ''; // Clear the input field
            saveChores(); // Save to local storage
        }
    };

    addButton.addEventListener('click', addChore);

    // Allow adding chores with Enter key
    choreInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            addChore();
        }
    });

    deleteButton.addEventListener('click', () => {
        if (confirm('Are you sure you want to delete ALL chores?')) {
            choreList.innerHTML = ''; // Clear all chores
            localStorage.removeItem('chores'); // Clear local storage
        }
    });

    // Add drag-and-drop support to the chore list container
    choreList.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
    });

    choreList.addEventListener('drop', (e) => {
        e.preventDefault();

        // If dropping on empty space, append to end
        if (draggedElement && e.target === choreList) {
            choreList.appendChild(draggedElement);

            // Re-add event listeners to the moved element
            addChoreEventListeners(draggedElement);
            addDragEventListeners(draggedElement);

            saveChores();
        }
    });

    // Theme toggle event listener
    themeToggle.addEventListener('click', toggleTheme);

    // Load theme and chores when the page is loaded
    loadTheme();
    loadChores();
});

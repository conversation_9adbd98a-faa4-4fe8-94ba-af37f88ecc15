document.addEventListener('DOMContentLoaded', () => {
    const addButton = document.getElementById('add-button');
    const deleteButton = document.getElementById('delete-button');
    const choreInput = document.getElementById('chore-input');
    const choreList = document.getElementById('chore-list');

    // Generate unique ID for chores
    const generateId = () => {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    };

    // Load chores from local storage
    const loadChores = () => {
        const chores = JSON.parse(localStorage.getItem('chores')) || [];
        choreList.innerHTML = ''; // Clear existing items
        chores.forEach(chore => {
            createChoreElement(chore);
        });
    };

    // Save chores to local storage
    const saveChores = () => {
        const chores = [];
        choreList.querySelectorAll('li').forEach(li => {
            const choreData = {
                id: li.dataset.choreId,
                text: li.querySelector('.chore-text').textContent,
                completed: li.querySelector('.chore-checkbox').checked
            };
            chores.push(choreData);
        });
        localStorage.setItem('chores', JSON.stringify(chores));
    };

    // Create a chore element with all functionality
    const createChoreElement = (chore) => {
        const choreItem = document.createElement('li');
        choreItem.dataset.choreId = chore.id;
        choreItem.className = chore.completed ? 'completed' : '';

        choreItem.innerHTML = `
            <input type="checkbox" class="chore-checkbox" ${chore.completed ? 'checked' : ''}>
            <span class="chore-text" contenteditable="false">${chore.text}</span>
            <button class="edit-button" title="Edit chore">✏️</button>
            <button class="delete-individual-button" title="Delete chore">🗑️</button>
        `;

        // Add event listeners for this chore
        addChoreEventListeners(choreItem);
        choreList.appendChild(choreItem);
    };

    // Add event listeners for individual chore functionality
    const addChoreEventListeners = (choreItem) => {
        const checkbox = choreItem.querySelector('.chore-checkbox');
        const choreText = choreItem.querySelector('.chore-text');
        const editButton = choreItem.querySelector('.edit-button');
        const deleteButton = choreItem.querySelector('.delete-individual-button');

        // Toggle completion status
        checkbox.addEventListener('change', () => {
            choreItem.classList.toggle('completed', checkbox.checked);
            saveChores();
        });

        // Edit chore functionality
        editButton.addEventListener('click', () => {
            if (choreText.contentEditable === 'false') {
                choreText.contentEditable = 'true';
                choreText.focus();
                editButton.textContent = '✅';
                editButton.title = 'Save changes';
            } else {
                choreText.contentEditable = 'false';
                editButton.textContent = '✏️';
                editButton.title = 'Edit chore';
                saveChores();
            }
        });

        // Save on Enter key when editing
        choreText.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                choreText.contentEditable = 'false';
                editButton.textContent = '✏️';
                editButton.title = 'Edit chore';
                saveChores();
            }
        });

        // Delete individual chore
        deleteButton.addEventListener('click', () => {
            if (confirm('Are you sure you want to delete this chore?')) {
                choreItem.remove();
                saveChores();
            }
        });
    };

    // Add new chore
    const addChore = () => {
        const choreText = choreInput.value.trim();
        if (choreText !== '') {
            const newChore = {
                id: generateId(),
                text: choreText,
                completed: false
            };
            createChoreElement(newChore);
            choreInput.value = ''; // Clear the input field
            saveChores(); // Save to local storage
        }
    };

    addButton.addEventListener('click', addChore);

    // Allow adding chores with Enter key
    choreInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            addChore();
        }
    });

    deleteButton.addEventListener('click', () => {
        if (confirm('Are you sure you want to delete ALL chores?')) {
            choreList.innerHTML = ''; // Clear all chores
            localStorage.removeItem('chores'); // Clear local storage
        }
    });

    // Load chores when the page is loaded
    loadChores();
});

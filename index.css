:root {
    --white: #FEFEFE;
    --black: #000000;
    --text-input-bg: #EEEEEE;
    --add-button: #FFCC68;
    --delete-button: #FF9548;
    --chore: #FBDF7C;
    --box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.5);
}

html, body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif;
    background-color: var(--white);
    color: var(--black);
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

h1 {
    font-family: 'Concert One', cursive;
    margin-bottom: 20px;
    font-size: 70px;
}

button {
    font-family: 'Inter', sans-serif;
    cursor: pointer;
    border-radius: 10px;
    padding: 20px;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    width: 300px;
    text-align: center;
}

.input-button-container {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

input[type="text"] {
    background-color: var(--text-input-bg);
    border: none;
    padding: 20px;
    margin-right: 10px;
    width: 300px;
    border-radius: 10px;
    text-align: center;
    font-size: 20px;
}

.add-button {
    background-color: var(--add-button);
    border: none;
    padding: 20px;
    margin-right: 5px;
    color: var(--black);
}

.delete-button {
    background-color: var(--delete-button);
    border: none;
    padding: 20px;
    color: var(--black);
}

ul {
    display: flex;
    flex-direction: row;
    list-style-type: none;
    padding: 0;
    gap: 15px;
    flex-wrap: wrap;
    width: 100%;
    justify-content: center;
}

li {
    font-size: 20px;
    background-color: var(--chore);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    box-shadow: var(--box-shadow);
    flex: 0 1 auto;
    min-width: 450px;
    max-width: 100%;
    width: calc(50% - 15px);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
}

li span {
    word-break: break-word;
    padding: 0 10px;
}

ul li:hover {
    background-color: #FF9548;
    cursor: pointer;
}

/* Media Queries for Responsiveness */
@media (max-width: 600px) {
    h1 {
        font-size: 50px;
    }

    .container {
        width: 90%;
        padding: 10px;
    }

    input[type="text"] {
        width: 100%;
        padding: 15px;
        font-size: 18px;
    }

    .add-button, .delete-button {
        padding: 15px;
        margin-right: 5px;
    }

    li {
        padding: 10px;
        font-size: 16px;
        min-width: 300px;
        width: 68%;
    }
}

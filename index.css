:root {
    --white: #FEFEFE;
    --black: #000000;
    --text-input-bg: #EEEEEE;
    --add-button: #FFCC68;
    --delete-button: #FF9548;
    --chore: #FBDF7C;
    --box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.5);
}

html, body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif;
    background-color: var(--white);
    color: var(--black);
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

h1 {
    font-family: 'Concert One', cursive;
    margin-bottom: 20px;
    font-size: 70px;
}

button {
    font-family: 'Inter', sans-serif;
    cursor: pointer;
    border-radius: 10px;
    padding: 20px;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    width: 300px;
    text-align: center;
}

.input-button-container {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

input[type="text"] {
    background-color: var(--text-input-bg);
    border: none;
    padding: 20px;
    margin-right: 10px;
    width: 300px;
    border-radius: 10px;
    text-align: center;
    font-size: 20px;
}

.add-button {
    background-color: var(--add-button);
    border: none;
    padding: 20px;
    margin-right: 5px;
    color: var(--black);
}

.delete-button {
    background-color: var(--delete-button);
    border: none;
    padding: 20px;
    color: var(--black);
}

ul {
    display: flex;
    flex-direction: row;
    list-style-type: none;
    padding: 0;
    gap: 15px;
    flex-wrap: wrap;
    width: 100%;
    justify-content: center;
}

li {
    font-size: 20px;
    background-color: var(--chore);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    box-shadow: var(--box-shadow);
    flex: 0 1 auto;
    min-width: 450px;
    max-width: 100%;
    width: calc(50% - 15px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    gap: 10px;
    transition: all 0.3s ease;
}

li.completed {
    background-color: #d4edda;
    opacity: 0.7;
}

li.completed .chore-text {
    text-decoration: line-through;
    color: #6c757d;
}

.chore-checkbox {
    width: 20px;
    height: 20px;
    cursor: pointer;
    flex-shrink: 0;
}

.chore-text {
    word-break: break-word;
    padding: 0 10px;
    flex-grow: 1;
    text-align: left;
    outline: none;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.chore-text[contenteditable="true"] {
    background-color: rgba(255, 255, 255, 0.8);
    border: 2px solid var(--add-button);
    padding: 5px 10px;
}

.edit-button, .delete-individual-button {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: 5px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    flex-shrink: 0;
}

.edit-button:hover {
    background-color: rgba(255, 204, 104, 0.3);
}

.delete-individual-button:hover {
    background-color: rgba(255, 149, 72, 0.3);
}

ul li:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
}

/* Drag and Drop Styles */
.drag-handle {
    color: #999;
    cursor: grab;
    font-size: 18px;
    padding: 0 5px;
    flex-shrink: 0;
    user-select: none;
}

.drag-handle:hover {
    color: #666;
}

.drag-handle:active {
    cursor: grabbing;
}

li.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    z-index: 1000;
}

li.drag-over-top {
    border-top: 3px solid var(--add-button);
}

li.drag-over-bottom {
    border-bottom: 3px solid var(--add-button);
}

/* Prevent text selection during drag */
li[draggable="true"] {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Media Queries for Responsiveness */
@media (max-width: 600px) {
    h1 {
        font-size: 50px;
    }

    .container {
        width: 90%;
        padding: 10px;
    }

    input[type="text"] {
        width: 100%;
        padding: 15px;
        font-size: 18px;
    }

    .add-button, .delete-button {
        padding: 15px;
        margin-right: 5px;
    }

    li {
        padding: 10px;
        font-size: 16px;
        min-width: 300px;
        width: 68%;
        gap: 5px;
    }

    .chore-text {
        font-size: 14px;
        padding: 0 5px;
    }

    .edit-button, .delete-individual-button {
        font-size: 14px;
        padding: 3px 6px;
    }

    .chore-checkbox {
        width: 16px;
        height: 16px;
    }

    .drag-handle {
        font-size: 14px;
        padding: 0 3px;
    }
}
